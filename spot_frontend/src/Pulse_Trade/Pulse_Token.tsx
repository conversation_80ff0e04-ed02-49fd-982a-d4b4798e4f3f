import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import usePulseData from "@/hooks/usePulseData";
import { useBackendTradeData } from "@/hooks/useBackendTradeData";
import { Clipboard } from "lucide-react";
import { toast } from "react-toastify";
import {
  Globe,
  Link2,
  Pen,
  Search,
  Star,
  Share2,
  Copy,
} from "lucide-react";
import TokenAge from "@/components/TokenAge";
import ShareModal from "./ShareModal";

export default function Pulse_Token() {
  const [showShareModal, setShowShareModal] = useState(false);
  const { address } = useParams<{ address: string }>();
  const { pulseTokens, removePulseToken, fetchAndAddTokenByAddress } = usePulseData();
  const [token, setToken] = useState<any>(null);
  const [isStarred, setIsStarred] = useState(false);

  // Get active token data from localStorage for pool address
  const [activeTokenData, setActiveTokenData] = useState<any>(null);

  // Get live trade data for real-time token stats (using backend WebSocket)
  // Use the active token's pool_address if available, otherwise use the token address
  // Only connect if we have a valid pool address (not just the token address)
  const poolAddress = activeTokenData?.pool_address || token?.pool_address;
  const fallbackAddress = poolAddress || address; // Use address as fallback only
  const { isConnected, latestRawTrade } = useBackendTradeData(fallbackAddress);
  const [liveTokenData, setLiveTokenData] = useState<any>(null);

  // Debug log for pool address and connection status
  useEffect(() => {
    console.log('🔗 [LIQUIDITY DEBUG] WebSocket Connection Info:', {
      poolAddress,
      fallbackAddress,
      isConnected,
      hasLatestRawTrade: !!latestRawTrade,
      activeTokenPoolAddress: activeTokenData?.pool_address,
      tokenPoolAddress: token?.pool_address,
      addressParam: address,
      connectionSource: activeTokenData?.pool_address ? 'activeToken' :
                      token?.pool_address ? 'token' : 'address_fallback',
      usingCorrectPoolAddress: !!poolAddress
    });
  }, [poolAddress, fallbackAddress, isConnected, latestRawTrade, activeTokenData, token, address]);





  // Load active token data from localStorage
  useEffect(() => {
    try {
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (activePulseTokenStr) {
        const activePulseToken = JSON.parse(activePulseTokenStr);
        setActiveTokenData(activePulseToken);
        console.log('🔄 [LIQUIDITY DEBUG] Loaded active token from localStorage:', {
          symbol: activePulseToken.symbol,
          address: activePulseToken.address || activePulseToken.id,
          pool_address: activePulseToken.pool_address,
          hasPoolAddress: !!activePulseToken.pool_address
        });
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken from localStorage:', error);
    }
  }, [address]);

  useEffect(() => {
    if (!address) return;

    // Search pulseTokens for token with matching address or id
    const foundToken = pulseTokens.find(t => t.address === address || t.id === address);

    if (foundToken) {
      setToken(foundToken);
      setIsStarred(true); // since token is in wishlist if found

      // Debug log to check token data structure
      console.log('🔍 [LIQUIDITY DEBUG] Found token data:', {
        symbol: foundToken.symbol,
        address: foundToken.address,
        pool_address: foundToken.pool_address,
        hasPoolAddress: !!foundToken.pool_address,
        liquidity: foundToken.liquidity,
        allTokenFields: Object.keys(foundToken)
      });

    } else {
      setToken(null);
      setIsStarred(false);
      console.log('🔍 [LIQUIDITY DEBUG] Token not found in pulseTokens for address:', address);

      // Fetch token data from API to get pool_address and other details
      if (address) {
        console.log('🔄 [LIQUIDITY DEBUG] Fetching token data from API for address:', address);
        fetchAndAddTokenByAddress(address, 'solana').then((success) => {
          if (success) {
            console.log('✅ [LIQUIDITY DEBUG] Successfully fetched token data from API');
            // The token should now be available in pulseTokens on the next render
          } else {
            console.log('❌ [LIQUIDITY DEBUG] Failed to fetch token data from API');
          }
        });
      }
    }
  }, [address, pulseTokens]);

  // Extract live token data from the latest raw trade
  useEffect(() => {
    if (latestRawTrade && isConnected) {
      // Debug log to check actual data structure
      console.log('🔍 [LIQUIDITY DEBUG] Received trade data structure:', {
        hasPairData: !!latestRawTrade.pairData,
        pairDataLiquidity: latestRawTrade.pairData?.liquidity,
        directLiquidity: latestRawTrade.liquidity,
        pairDataKeys: latestRawTrade.pairData ? Object.keys(latestRawTrade.pairData) : [],
        liquidityType: typeof latestRawTrade.pairData?.liquidity
      });

      // Helper function to determine which token is SOL and which is the actual token
      const SOL_ADDRESS = 'So11111111111111111111111111111111111111112';
      let actualToken = null;

      if (latestRawTrade.pairData?.token0 && latestRawTrade.pairData?.token1) {
        const token0 = latestRawTrade.pairData.token0;
        const token1 = latestRawTrade.pairData.token1;

        // Check if token1 has SOL address, then token0 is the actual token
        if (token1.address === SOL_ADDRESS) {
          actualToken = token0;
        }
        // Check if token0 has SOL address, then token1 is the actual token
        else if (token0.address === SOL_ADDRESS) {
          actualToken = token1;
        }
        // Default fallback - assume token1 is the actual token
        else {
          actualToken = token1;
        }
      }

      // Extract live data from the latest raw trade
      // Priority for price: token_price (most accurate) > actualToken.price > pairData.price > trade.price
      let tokenPrice = null;

      // Helper function to parse price from string or number
      const parsePrice = (priceValue: any): number | null => {
        if (typeof priceValue === 'number' && priceValue > 0) {
          return priceValue;
        }
        if (typeof priceValue === 'string') {
          // Remove $ and other currency symbols, then parse
          const cleanPrice = priceValue.replace(/[$,]/g, '');
          const parsed = parseFloat(cleanPrice);
          return !isNaN(parsed) && parsed > 0 ? parsed : null;
        }
        return null;
      };

      // Try to extract price from various fields, parsing strings if needed
      tokenPrice = parsePrice(latestRawTrade.token_price) ||
                  parsePrice(actualToken?.price) ||
                  parsePrice(latestRawTrade.pairData?.price) ||
                  parsePrice(latestRawTrade.price) ||
                  parsePrice(latestRawTrade.pairData?.token0?.price) ||
                  parsePrice(latestRawTrade.pairData?.token1?.price) ||
                  null;

      // Helper function to parse liquidity from string or number
      const parseLiquidity = (liquidityValue: any): number | null => {
        if (typeof liquidityValue === 'number' && liquidityValue > 0) {
          return liquidityValue;
        }
        if (typeof liquidityValue === 'string') {
          // Remove $ and other currency symbols, then parse
          const cleanLiquidity = liquidityValue.replace(/[$,]/g, '');
          const parsed = parseFloat(cleanLiquidity);
          return !isNaN(parsed) && parsed > 0 ? parsed : null;
        }
        return null;
      };

      // Extract liquidity from pairData (live data from WebSocket)
      // Based on the comprehensive Mobula data structure, liquidity should be directly available
      let liquidityValue = parseLiquidity(latestRawTrade.pairData?.liquidity) || null;

      // If no liquidity in pairData, check if it's available directly on the trade object
      if (!liquidityValue && latestRawTrade.liquidity) {
        liquidityValue = parseLiquidity(latestRawTrade.liquidity);
      }



      // Helper function to parse supply from string or number
      const parseSupply = (supplyValue: any): number | null => {
        if (typeof supplyValue === 'number' && supplyValue > 0) {
          return supplyValue;
        }
        if (typeof supplyValue === 'string') {
          // Remove commas and other formatting, then parse
          const cleanSupply = supplyValue.replace(/[,]/g, '');
          const parsed = parseFloat(cleanSupply);
          return !isNaN(parsed) && parsed > 0 ? parsed : null;
        }
        return null;
      };

      // Extract supply from various possible fields
      let supplyValue = parseSupply(actualToken?.totalSupply) ||
                       parseSupply(latestRawTrade.pairData?.token0?.totalSupply) ||
                       parseSupply(latestRawTrade.pairData?.token1?.totalSupply) ||
                       parseSupply(latestRawTrade.totalSupply) ||
                       parseSupply(latestRawTrade.supply) ||
                       0;

      const extractedData = {
        marketCap: actualToken?.marketCap || latestRawTrade.marketCap || 0,
        price: tokenPrice, // Keep as null if no price found, so fallback to static price works
        liquidity: liquidityValue, // Live liquidity data from WebSocket pairData
        supply: supplyValue
      };

      // Debug log to verify liquidity extraction
      console.log('📊 [LIQUIDITY DEBUG] Final extracted data:', {
        extractedData,
        liquidityValue,
        hasLiveLiquidity: !!liquidityValue,
        staticTokenLiquidity: token?.liquidity,
        willUseLiveData: !!liquidityValue,
        // Additional debugging for liquidity sources
        pairDataLiquidityRaw: latestRawTrade.pairData?.liquidity,
        directLiquidityRaw: latestRawTrade.liquidity,
        liquidityParsingResult: {
          fromPairData: parseLiquidity(latestRawTrade.pairData?.liquidity),
          fromDirect: parseLiquidity(latestRawTrade.liquidity)
        }
      });

      setLiveTokenData(extractedData);
    }
  }, [latestRawTrade, isConnected]);

  // Copy address to clipboard handler (unchanged)
  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!address) return;
    navigator.clipboard.writeText(address);
    toast(
      <div className="flex items-center gap-3">
        <Clipboard className="text-indigo-400 w-5 h-5" />
        <span>Address copied to clipboard</span>
      </div>,
      {
        autoClose: 2000,
        closeButton: true,
        hideProgressBar: true,
        className:
          "bg-[#1f1f1f] text-white font-medium border border-gray-700 rounded-lg shadow-md",
        icon: false,
      }
    );
  };

  // Toggle starred status (add/remove from pulseTokens)
  const toggleStar = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!token) return;

    if (isStarred) {
      removePulseToken(token.id);
      setIsStarred(false);
    } else {
      // To add, you can use setActiveTokenFromWishlist or create an add function in your hook
      // For example:
   
      setIsStarred(true);
    }
  };

  if (!token) {
    return <div className="text-white p-4">Loading token...</div>;
  }
  const formatNumber = (value: number) => {
    if (value === null || value === undefined || isNaN(value)) return "N/A";
    if (value >= 1_000_000_000) return `${(value / 1_000_000_000).toFixed(2)}B`;
    if (value >= 1_000_000) return `${(value / 1_000_000).toFixed(2)}M`;
    if (value >= 1_000) return `${(value / 1_000).toFixed(2)}K`;
    return value.toFixed(2);
  };

  const formatLowPrice = (price: number | undefined): string => {
    if (price === undefined || price === null || isNaN(price)) return "N/A";

    // For normal price range (0.01 and above)
    if (price >= 0.01) {
      return price.toFixed(4);
    }

    // Convert to string to analyze the decimal structure
    const priceStr = price.toString();

    // Handle scientific notation (e.g., 2.3847510365339614e-12)
    if (priceStr.includes('e-')) {
      const parts = priceStr.split('e-');
      const significantDigits = parts[0].replace('.', '');
      const exponent = parseInt(parts[1]);

      // For very small numbers like 2.3847510365339614e-12
      const leadingZeros = exponent - 1;
      const significantPart = significantDigits.substring(0, 4); // Take first 4 significant digits

      // Use subscript numbers for leading zeros
      const subscriptNumbers = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉'];
      const subscriptZeros = leadingZeros.toString().split('').map(digit => subscriptNumbers[parseInt(digit)]).join('');

      return `0.0${subscriptZeros}${significantPart}`;
    }

    // For decimal notation (non-scientific) like 0.000003295654485242278
    if (priceStr.includes('.')) {
      const decimalPart = priceStr.split('.')[1];
      let leadingZeros = 0;

      // Count leading zeros after decimal point
      for (let i = 0; i < decimalPart.length; i++) {
        if (decimalPart[i] === '0') {
          leadingZeros++;
        } else {
          break;
        }
      }



      // If we have leading zeros, use subscript format
      if (leadingZeros > 0) {
        // Get the significant digits after leading zeros
        const significantPart = decimalPart.substring(leadingZeros, leadingZeros + 4);

        // Use subscript numbers for leading zeros
        const subscriptNumbers = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉'];
        const subscriptZeros = leadingZeros.toString().split('').map(digit => subscriptNumbers[parseInt(digit)]).join('');

        return `0.0${subscriptZeros}${significantPart}`;
      } else {
        // No leading zeros, just format normally
        return price.toFixed(6);
      }
    }

    // Fallback
    return price.toFixed(6);
  };
  
  return (
    <div className="text-white p-4 flex items-center justify-between border border-[#2a2a2e] h-full">
      {/* LEFT: Token Info */}
      <div className="flex items-center gap-4">
        <div className="relative">
          <img
            src={token.imageUrl}
            alt={token.name}
            className="w-12 h-12 rounded-md object-cover"
          />
          <span className="absolute -bottom-1 -right-1 bg-[#141416] border border-green-500 rounded-full p-[2px]">
            <img
              src={token.exchange_logo}
              alt={`${token.name} exchange logo`}
              className="w-3 h-3 rounded-full object-cover"
            />
          </span>
        </div>

        <div className="flex flex-col justify-center">
          <div className="flex items-center gap-2">
            <div className="text-lg font-semibold">{token.name}</div>
            <div className="text-gray-500 text-sm">{token.symbol}</div>
            <Copy
              onClick={handleCopy}
              className="w-4 h-4 cursor-pointer text-gray-500 hover:text-[#6683FF] transition-colors"
            />
          </div>

          <TokenAge
            createdAt={token.createdAt}
            className="text-xs font-semibold"
          />

          <div className="flex items-center gap-2 mt-1 text-gray-300">
            <Link2 size={14} className="hover:text-white cursor-pointer" />
            <Pen size={14} className="hover:text-white cursor-pointer" />
            <Globe size={14} className="hover:text-white cursor-pointer" />
            <Search size={14} className="hover:text-white cursor-pointer" />
          </div>
        </div>
      </div>

      {/* MIDDLE: Stats */}
      <div className="flex gap-10 items-center">
        <div className="text-center">
          <div className="text-xs text-gray-400 mb-1">Market Cap</div>
          <div className="text-md font-semibold">
            ${formatNumber(liveTokenData?.marketCap || token.market_cap)}
          </div>
        </div>

        <div className="text-center">
          <div className="text-xs text-gray-400 mb-1">Price</div>
          <div className="text-md font-semibold">
            ${formatLowPrice(liveTokenData?.price || token.price)}

          </div>
        </div>

        <div className="text-center">
          <div className="text-xs text-gray-400 mb-1">Liquidity</div>
          <div className="text-md font-semibold">
            ${formatNumber(liveTokenData?.liquidity || token.liquidity)}
          </div>
        </div>

        <div className="text-center">
          <div className="text-xs text-gray-400 mb-1">Supply</div>
          <div className="text-md font-semibold">{formatNumber(liveTokenData?.supply || token.supply)}</div>
        </div>

        {/* {token.bonding_percent && token.bonding_percent > 0 && (
          <div className="text-center">
            <div className="text-xs text-gray-400 mb-1">B.Curve</div>
            <div className="text-md font-semibold text-green-500">
              {token.bonding_percent}%
            </div>
          </div>
        )} */}
      </div>

      {/* RIGHT: Actions */}
      <div className="flex items-center gap-4 text-gray-300 ml-4">
        <Share2 onClick={() => setShowShareModal(true)} className="hover:text-white cursor-pointer" />
        <Star
          onClick={toggleStar}
          className={`cursor-pointer transition-colors ${
            isStarred ? "text-yellow-400" : "text-gray-400 hover:text-white"
          }`}
        />
      </div>
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        token={token}
        liveTokenData={liveTokenData}
      />
    </div>
  );
}
